# Use the official Python runtime as a parent image
FROM python:3.11-slim

# Set the working directory in the container
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY . /app

# Install any needed packages specified in requirements.txt
# (uncomment the next line if you have a requirements.txt file)
# RUN pip install --no-cache-dir -r requirements.txt

# Make port 80 available to the world outside this container
# (uncomment if your app needs to expose a port)
# EXPOSE 80

# Define environment variable
ENV NAME World

# Run hello.py when the container launches
CMD ["python", "hello.py"]
